from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
# from webdriver_manager.microsoft import EdgeChromiumDriverManager
from selenium.webdriver.common.keys import Keys
import time
import os
import traceback
from logger_config import Logger
from datetime import datetime
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Select

class AdobeAutomation:
    def __init__(self):
        # 初始化日志记录器
        self.logger = Logger().get_logger()

        # 设置 WebDriver 路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        venv_path = os.path.join(current_dir, 'venv')
        driver_path = os.path.join(venv_path, 'webdriver')
        os.environ['WDM_LOCAL_PATH'] = driver_path
        os.environ['WDM_LOG_LEVEL'] = '0'  # 禁用日志输出

        # 从文件读取邮箱数据
        self.test_data = self.load_email_data()

        # 可选的自定义Edge路径，设置为None表示使用自动检测，默认为：self.edge_path = None
        self.edge_path = None  # 如需指定路径，直接修改这里，例如：r"C:\Edge\Application\msedge.exe"

    def load_email_data(self):
        """从 youxiang.txt 文件加载邮箱数据"""
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(current_dir, 'edge.txt')

            if not os.path.exists(file_path):
                self.logger.error(f"错误：未找到邮箱数据文件 {file_path}")
                return []

            with open(file_path, 'r', encoding='utf-8') as f:
                # 读取所有行，去除空白字符，过滤掉空行
                emails = [line.strip() for line in f.readlines() if line.strip()]

            self.logger.info(f"成功加载 {len(emails)} 个邮箱地址")
            return emails

        except Exception as e:
            self.logger.error(f"读取邮箱数据文件时发生错误: {e}")
            return []

    def setup_driver(self):
        max_retries = 3  # 最大重试次数
        retry_delay = 2  # 重试间隔（秒）

        for attempt in range(max_retries):
            try:
                edge_options = Options()

                # 默认的Edge安装路径列表
                default_edge_paths = [
                    r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                    r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
                    os.path.expanduser('~') + r"\AppData\Local\Microsoft\Edge\Application\msedge.exe",
                ]

                # 如果设置了自定义路径，优先使用
                if self.edge_path and os.path.exists(self.edge_path):
                    edge_options.binary_location = self.edge_path
                    self.logger.info(f"使用指定的Edge路径: {self.edge_path}")
                else:
                    # 自动检测Edge路径
                    edge_found = False
                    for path in default_edge_paths:
                        if os.path.exists(path):
                            edge_options.binary_location = path
                            self.logger.info(f"自动检测到Edge路径: {path}")
                            edge_found = True
                            break

                    if not edge_found:
                        self.logger.warning("未找到Edge浏览器，将使用系统默认Edge位置")

                edge_options.add_argument('-inprivate')
                edge_options.add_argument('--disable-gpu')
                edge_options.add_argument('--no-sandbox')
                edge_options.add_argument('--disable-dev-shm-usage')
                edge_options.add_argument('--disable-software-rasterizer')
                edge_options.add_argument('--disable-extensions')
                edge_options.add_argument('--disable-logging')
                edge_options.add_argument('--log-level=3')
                edge_options.add_argument('--silent')
                edge_options.add_experimental_option('excludeSwitches', ['enable-logging'])

                # 添加代理设置（如果需要）
                # edge_options.add_argument('--proxy-server=http://your-proxy:port')

                # 使用本地webdriver驱动
                current_dir = os.path.dirname(os.path.abspath(__file__))
                venv_path = os.path.join(current_dir, 'venv')
                driver_path = os.path.join(venv_path, 'webdriver')

                # 寻找本地msedgedriver.exe
                driver_files = [f for f in os.listdir(driver_path) if f.startswith('msedgedriver')]

                if not driver_files:
                    self.logger.error("未在venv/webdriver目录中找到Edge驱动文件")
                    raise FileNotFoundError("未找到msedgedriver驱动文件")

                # 使用找到的第一个驱动文件
                edge_driver_path = os.path.join(driver_path, driver_files[0])
                self.logger.info(f"使用本地Edge驱动: {edge_driver_path}")

                service = Service(edge_driver_path)
                service.log_path = 'NUL'  # Windows

                driver = webdriver.Edge(service=service, options=edge_options)

                # 测试连接
                driver.get('about:blank')
                return driver

            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"第 {attempt + 1} 次尝试设置浏览器失败: {str(e)}")
                    self.logger.info(f"等待 {retry_delay} 秒后重试...")

                    # 如果有正在运行的驱动，先关闭它
                    try:
                        if 'driver' in locals():
                            driver.quit()
                    except Exception:
                        pass

                    time.sleep(retry_delay)
                    continue
                else:
                    self.logger.error(f"设置浏览器失败，已达到最大重试次数: {str(e)}")
                    raise

    def wait_and_find_element(self, driver, css_selector, timeout=10):
        return WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, css_selector))
        )

    def wait_and_find_element_dynamic(self, driver, selectors, timeout=10):
        """
        尝试多个选择器来查找动态元素
        selectors: 选择器列表，按优先级排序
        """
        for selector in selectors:
            try:
                element = WebDriverWait(driver, timeout // len(selectors)).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                self.logger.info(f"成功找到元素，使用选择器: {selector}")
                return element
            except Exception as e:
                self.logger.warning(f"选择器 {selector} 未找到元素: {str(e)}")
                continue

        # 如果所有选择器都失败，抛出异常
        raise Exception(f"无法找到元素，尝试的选择器: {selectors}")

    def wait_and_find_clickable_element_dynamic(self, driver, selectors, timeout=10):
        """
        尝试多个选择器来查找可点击的动态元素
        selectors: 选择器列表，按优先级排序
        """
        for selector in selectors:
            try:
                element = WebDriverWait(driver, timeout // len(selectors)).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                self.logger.info(f"成功找到可点击元素，使用选择器: {selector}")
                return element
            except Exception as e:
                self.logger.warning(f"选择器 {selector} 未找到可点击元素: {str(e)}")
                continue

        # 如果所有选择器都失败，抛出异常
        raise Exception(f"无法找到可点击元素，尝试的选择器: {selectors}")

    def find_element_by_text(self, driver, text, tag_names=None, timeout=10):
        """
        通过文本内容查找元素
        text: 要查找的文本
        tag_names: 标签名列表，如果为None则查找所有标签
        """
        if tag_names is None:
            tag_names = ['span', 'div', 'button', 'a', 'li']

        for tag in tag_names:
            try:
                xpath = f"//{tag}[contains(text(), '{text}')]"
                element = WebDriverWait(driver, timeout // len(tag_names)).until(
                    EC.element_to_be_clickable((By.XPATH, xpath))
                )
                self.logger.info(f"通过文本'{text}'在{tag}标签中找到元素")
                return element
            except Exception:
                continue

        raise Exception(f"无法通过文本'{text}'找到元素")

    def wait_for_page_load(self, driver):
        WebDriverWait(driver, 10).until(
            lambda driver: driver.execute_script('return document.readyState') == 'complete'
        )

    def process_email(self, email):
        max_retries = 2  # 最大重试次数
        retry_count = 0

        while retry_count < max_retries:
            driver = None
            try:
                self.logger.info(f"开始处理邮箱: {email} (尝试 {retry_count + 1}/{max_retries})")
                driver = self.setup_driver()

                # 1. 打开Adobe登录页面
                driver.get("https://auth.services.adobe.com/en_US/index.html?callback=https%3A%2F%2Fims-na1.adobelogin.com%2Fims%2Fadobeid%2Fhomepage_milo%2FAdobeID%2Ftoken%3Fredirect_uri%3Dhttps%253A%252F%252Fwww.adobe.com%252Fhome%2523old_hash%253D%2526from_ims%253Dtrue%253Fclient_id%253Dhomepage_milo%2526api%253Dauthorize%2526scope%253DAdobeID%252Copenid%252Cgnav%252Cpps.read%252Cfirefly_api%252Cadditional_info.roles%252Cread_organizations%252Caccount_cluster.read%26state%3D%257B%2522jslibver%2522%253A%2522v2-v0.46.0-19-g35c1ff9%2522%252C%2522nonce%2522%253A%25223242348474200921%2522%257D%26code_challenge_method%3Dplain%26use_ms_for_expiry%3Dtrue&client_id=homepage_milo&scope=AdobeID%2Copenid%2Cgnav%2Cpps.read%2Cfirefly_api%2Cadditional_info.roles%2Cread_organizations%2Caccount_cluster.read&state=%7B%22jslibver%22%3A%22v2-v0.46.0-19-g35c1ff9%22%2C%22nonce%22%3A%223242348474200921%22%7D&relay=5b379341-3484-4992-b8fd-f43c7ed2c5d4&locale=en_US&flow_type=token&idp_flow_type=login&ab_test=epp-avoid-race-condition&s_p=google%2Cfacebook%2Capple%2Cmicrosoft%2Cline%2Ckakao&response_type=token&code_challenge_method=plain&redirect_uri=https%3A%2F%2Fwww.adobe.com%2Fhome%23old_hash%3D%26from_ims%3Dtrue%3Fclient_id%3Dhomepage_milo%26api%3Dauthorize%26scope%3DAdobeID%2Copenid%2Cgnav%2Cpps.read%2Cfirefly_api%2Cadditional_info.roles%2Cread_organizations%2Caccount_cluster.read&use_ms_for_expiry=true#/")
                self.wait_for_page_load(driver)
                time.sleep(2)

                # 3-5. 输入邮箱并点击
                email_field = self.wait_and_find_element(driver, "input#EmailPage-EmailField")
                email_field.send_keys(email)
                time.sleep(1)
                continue_button = self.wait_and_find_element(driver, "button[data-id='EmailPage-ContinueButton']")
                continue_button.click()

                # 6-7. 等待并点击
                time.sleep(5)
                button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-id='AdditionalAccountDetailsPage-ContinueButton']"))
                )

                driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});", button)
                time.sleep(1)

                action = ActionChains(driver)
                action.move_to_element(button)
                time.sleep(0.5)
                action.click()
                action.perform()

                # 8-9. 打开新标签页
                time.sleep(4)
                driver.execute_script("window.open('https://cha3.abpsai.top');")
                driver.switch_to.window(driver.window_handles[-1])

                # 10-14. 登录邮箱
                time.sleep(8)
                self.wait_and_find_element(driver, "input#emailInput").send_keys(email)
                time.sleep(1)
                self.wait_and_find_element(driver, "button.search-button").click()

                # 15-18. 获取验证码
                time.sleep(5)

                try:
                    WebDriverWait(driver, 2).until(
                        lambda d: d.execute_script("return document.readyState") == "complete"
                    )

                    verification_code = WebDriverWait(driver, 1).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "p.verification-code"))
                    ).text

                    if not verification_code:
                        error_msg = "未能获取到验证码"
                        self.logger.error(error_msg)
                        self._save_error_log("process_email", error_msg, f"邮箱: {email}")
                        raise Exception(error_msg)

                    self.logger.info(f"成功获取验证码: {verification_code}")

                    time.sleep(1)

                except Exception as e:
                    self.logger.error(f"获取验证码时发生错误: {str(e)}")
                    self._save_error_log("process_email", f"获取验证码失败: {str(e)}", f"邮箱: {email}")
                    raise

                # 19-21. 切换回Adobe页面并输入验证码
                driver.switch_to.window(driver.window_handles[0])
                time.sleep(1)
                for i in range(6):
                    input_selector = f"input[data-id='CodeInput-{i}']"
                    digit_input = self.wait_and_find_element(driver, input_selector)
                    digit_input.send_keys(verification_code[i])
                    time.sleep(0.1)  # 短暂延迟以模拟人工输入

                # 23-28. 填写注册信息
                time.sleep(8)

                # 输入年份 - 使用动态选择器
                year_selectors = [
                    'input[data-id="DateOfBirthChooser-Year"]',
                    'input#react-aria2162479294-136',
                    'input[name="bday-year"]',
                    'input[autocomplete="bday-year"]'
                ]
                year_field = self.wait_and_find_element_dynamic(driver, year_selectors)
                year_field.send_keys("2000")

                # 输入First Name - 使用动态选择器
                firstname_selectors = [
                    'input[data-id="Signup-FirstNameField"]',
                    'input#Signup-FirstNameField',
                    'input[name="firstname"]',
                    'input[autocomplete="given-name"]'
                ]
                firstname_field = self.wait_and_find_element_dynamic(driver, firstname_selectors)
                firstname_field.send_keys("asdm")

                # 输入Last Name - 使用动态选择器
                lastname_selectors = [
                    'input[data-id="Signup-LastNameField"]',
                    'input#Signup-LastNameField',
                    'input[name="lastname"]',
                    'input[autocomplete="family-name"]'
                ]
                lastname_field = self.wait_and_find_element_dynamic(driver, lastname_selectors)
                lastname_field.send_keys("dfgre")

                # 输入密码 - 使用动态选择器
                password_selectors = [
                    'input[data-id="Signup-PasswordField"]',
                    'input#Signup-PasswordField',
                    'input[name="password"]',
                    'input[autocomplete="new-password"]',
                    'input[type="password"]'
                ]
                password_field = self.wait_and_find_element_dynamic(driver, password_selectors)
                password_field.send_keys("aA123456789..")
                time.sleep(3)

                # 29-36. 选择月份并提交
                try:
                    # 选择月份按钮 - 使用动态选择器
                    month_button_selectors = [
                        'button[data-id="DateOfBirthChooser-Month"]',
                        'button[aria-haspopup="listbox"]',
                        'button.ntVziG_spectrum-FieldButton',
                        'button[id*="react-aria"][aria-haspopup="listbox"]'
                    ]

                    month_button = self.wait_and_find_clickable_element_dynamic(driver, month_button_selectors, 10)
                    driver.execute_script("arguments[0].click();", month_button)
                    time.sleep(2)  # 等待下拉菜单展开

                    # 选择December选项 - 优先使用文本查找
                    try:
                        december_option = self.find_element_by_text(driver, "December", ['span', 'div', 'li'], 5)
                    except Exception:
                        # 如果文本查找失败，尝试CSS选择器
                        december_selectors = [
                            'span.dIo7iW_spectrum-Menu-itemLabel',
                            'span[class*="spectrum-Menu-itemLabel"]',
                            'span[class*="Menu-itemLabel"]',
                            '[role="option"] span'
                        ]
                        december_option = self.wait_and_find_clickable_element_dynamic(driver, december_selectors, 5)

                    driver.execute_script("arguments[0].scrollIntoView(true);", december_option)
                    time.sleep(1)  # 等待滚动完成
                    driver.execute_script("arguments[0].click();", december_option)
                    time.sleep(2)  # 等待选择完成

                except Exception as e:
                    error_message = f"选择月份时发生错误: {str(e)}"
                    self.logger.error(error_message)
                    self._save_error_log("process_email", error_message, traceback.format_exc())
                    raise

                time.sleep(2)
                # 点击提交按钮 - 使用动态选择器
                submit_button_selectors = [
                    'button[data-id="Signup-CreateAccountBtn"]',
                    'button[type="submit"]',
                    'button[class*="CreateAccount"]',
                    'button[class*="Submit"]'
                ]
                submit_button = self.wait_and_find_clickable_element_dynamic(driver, submit_button_selectors, 10)
                driver.execute_script("arguments[0].click();", submit_button)
                self.logger.info("成功点击注册按钮")

                # 37. 最终等待
                time.sleep(10)

                # 判断是否成功跳转到Adobe主页
                current_url = driver.current_url
                if "https://www.adobe.com/home" in current_url:
                    self.logger.info(f"邮箱 {email} 注册成功，已跳转到Adobe主页")
                    return True  # 成功处理完成
                else:
                    self.logger.error(f"注册失败，未跳转到Adobe主页")
                    self._save_error_log("process_email", "注册失败，未跳转到Adobe主页", f"邮箱: {email}")
                    self._save_failed_email(email)
                    return False  # 处理失败

            except Exception as e:
                retry_count += 1
                error_message = str(e)

                if retry_count == 1:
                    self.logger.warning(f"首次尝试失败，正在关闭Edge隐私窗口...")
                    try:
                        if driver:
                            driver.quit()
                        time.sleep(1)
                        self.logger.info("Edge隐私窗口已关闭，准备重试...")
                    except Exception as close_error:
                        self.logger.error(f"关闭Edge隐私窗口时发生错误: {close_error}")
                    continue

                if retry_count >= max_retries:
                    self._save_failed_email(email)
                    self.logger.error(f"邮箱 {email} 处理失败，已记录到失败文件")

            finally:
                if driver:
                    try:
                        driver.quit()
                    except Exception:
                        pass

        return False

    def _save_failed_email(self, email):
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            failed_file = os.path.join(current_dir, 'edge未成功.txt')

            with open(failed_file, 'a', encoding='utf-8') as f:
                f.write(f"{email}\n")

            self.logger.info(f"已将失败邮箱记录到: {failed_file}")

        except Exception as e:
            self.logger.error(f"保存失败邮箱记录时发生错误: {str(e)}")

    def _save_error_log(self, function_name, error_msg, additional_info=""):
        """记录错误到错误日志文件"""
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            error_log_file = os.path.join(current_dir, 'edge_errors.log')

            with open(error_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"[{timestamp}] 函数: {function_name}\n")
                f.write(f"错误信息: {error_msg}\n")
                if additional_info:
                    f.write(f"附加信息: {additional_info}\n")
                f.write("-" * 50 + "\n\n")

            self.logger.info(f"已将错误记录到: {error_log_file}")

        except Exception as e:
            self.logger.error(f"保存错误日志时发生错误: {str(e)}")

    def run(self):
        self.logger.info("开始运行自动化脚本")

        results = {
            'success': [],
            'failed': []
        }

        current_dir = os.path.dirname(os.path.abspath(__file__))
        failed_file = os.path.join(current_dir, 'edge未成功.txt')
        if os.path.exists(failed_file):
            os.remove(failed_file)

        for email in self.test_data:
            try:
                self.logger.info(f"正在处理邮箱: {email}")
                if self.process_email(email):
                    results['success'].append(email)
                    self.logger.info(f"邮箱 {email} 处理成功")
                else:
                    results['failed'].append(email)
                    self.logger.error(f"邮箱 {email} 处理失败")

            except Exception as e:
                results['failed'].append(email)
                self.logger.error(f"处理邮箱 {email} 时发生错误: {str(e)}")

            self.logger.info("等待处理下一个邮箱...\n")

        self._save_results(results)

        self.logger.info(f"自动化脚本运行完成")
        self.logger.info(f"成功: {len(results['success'])} 个")
        self.logger.info(f"失败: {len(results['failed'])} 个")

    def _save_results(self, results):
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            current_dir = os.path.dirname(os.path.abspath(__file__))

            results_dir = os.path.join(current_dir, 'results')
            os.makedirs(results_dir, exist_ok=True)

            result_file = os.path.join(results_dir, f'result_{timestamp}.txt')

            with open(result_file, 'w', encoding='utf-8') as f:
                f.write("=== 处理成功的邮箱 ===\n")
                for email in results['success']:
                    f.write(f"{email}\n")

                f.write("\n=== 处理失败的邮箱 ===\n")
                for email in results['failed']:
                    f.write(f"{email}\n")

            self.logger.info(f"处理结果已保存到: {result_file}")

        except Exception as e:
            self.logger.error(f"保存结果文件时发生错误: {str(e)}")

if __name__ == "__main__":
    try:
        print("正在启动自动化脚本...")
        automation = AdobeAutomation()
        print("初始化完成，开始运行...")
        automation.run()
    except Exception as e:
        print(f"程序发生错误: {str(e)}")
    finally:
        print("\n按回车键退出...")
        input()